import { Button } from '@/components/ui/button';
import { <PERSON>, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { Activity, Calendar, Heart, Shield, Stethoscope, Users } from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title="Welcome to Dr. Club">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
                {/* Navigation */}
                <header className="border-b bg-white/80 backdrop-blur-sm dark:bg-gray-900/80 dark:border-gray-800">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex h-16 items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
                                    <Stethoscope className="h-5 w-5 text-white" />
                                </div>
                                <span className="text-xl font-bold text-gray-900 dark:text-white">Dr. Club</span>
                            </div>
                            <nav className="flex items-center space-x-4">
                                {auth.user ? (
                                    <Button asChild>
                                        <Link href={route('dashboard')}>Dashboard</Link>
                                    </Button>
                                ) : (
                                    <>
                                        <Button variant="ghost" asChild>
                                            <Link href={route('login')}>Log in</Link>
                                        </Button>
                                        <Button asChild>
                                            <Link href={route('register')}>Get Started</Link>
                                        </Button>
                                    </>
                                )}
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Hero Section */}
                <main className="mx-auto max-w-7xl px-4 py-16 sm:px-6 sm:py-24 lg:px-8">
                    <div className="flex flex-col items-center justify-center h-[calc(56vh)] text-center">
                        <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl dark:text-white">
                            Welcome to{' '}
                            <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                                Dr. Club
                            </span>
                        </h1>
                        <div className="mt-10 flex items-center justify-center gap-x-6">
                            {auth.user ? (
                                <Button size="lg" asChild>
                                    <Link href={route('dashboard')}>Go to Dashboard</Link>
                                </Button>
                            ) : (
                                <>
                                    <Button size="lg" asChild>
                                        <Link href={route('register')}>Get Started</Link>
                                    </Button>
                                    <Button variant="outline" size="lg" asChild>
                                        <Link href={route('login')}>Sign In</Link>
                                    </Button>
                                </>
                            )}
                        </div>
                    </div>
                </main>

                {/* Footer */}
                <footer className="border-t bg-white/80 backdrop-blur-sm dark:bg-gray-900/80 dark:border-gray-800">
                    <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="flex items-center justify-center space-x-2">
                                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
                                    <Stethoscope className="h-5 w-5 text-white" />
                                </div>
                                <span className="text-xl font-bold text-gray-900 dark:text-white">Dr. Club</span>
                            </div>
                            <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                                {new Date().getFullYear()} © Dr. Club. All rights reserved. Empowering healthcare through technology.
                            </p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}